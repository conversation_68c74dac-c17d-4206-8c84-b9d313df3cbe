package com.classschedule.app.utils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;

/**
 * 日期工具类
 * 提供日期相关的工具方法
 */
public class DateUtils {
    
    /**
     * 获取当前日期的格式化字符串
     * @return 格式：周X X月X号
     */
    public static String getCurrentDateString() {
        Calendar calendar = Calendar.getInstance();
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        int month = calendar.get(Calendar.MONTH) + 1; // Calendar.MONTH 从0开始
        int dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH);
        
        String weekDay = getWeekDayString(dayOfWeek);
        return String.format(Locale.CHINA, "%s %d月%d号", weekDay, month, dayOfMonth);
    }
    
    /**
     * 根据Calendar的星期值获取中文星期字符串
     * @param dayOfWeek Calendar.DAY_OF_WEEK的值
     * @return 中文星期字符串
     */
    public static String getWeekDayString(int dayOfWeek) {
        switch (dayOfWeek) {
            case Calendar.SUNDAY:
                return "周日";
            case Calendar.MONDAY:
                return "周一";
            case Calendar.TUESDAY:
                return "周二";
            case Calendar.WEDNESDAY:
                return "周三";
            case Calendar.THURSDAY:
                return "周四";
            case Calendar.FRIDAY:
                return "周五";
            case Calendar.SATURDAY:
                return "周六";
            default:
                return "周一";
        }
    }
    
    /**
     * 根据我们的星期值(1-7)获取中文星期字符串
     * @param dayOfWeek 1-7，1为周一
     * @return 中文星期字符串
     */
    public static String getWeekDayStringByIndex(int dayOfWeek) {
        switch (dayOfWeek) {
            case 1:
                return "周一";
            case 2:
                return "周二";
            case 3:
                return "周三";
            case 4:
                return "周四";
            case 5:
                return "周五";
            case 6:
                return "周六";
            case 7:
                return "周日";
            default:
                return "周一";
        }
    }
    
    /**
     * 获取当前是星期几(1-7，1为周一)
     * @return 星期几
     */
    public static int getCurrentDayOfWeek() {
        Calendar calendar = Calendar.getInstance();
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        
        // Calendar.DAY_OF_WEEK: 周日=1, 周一=2, ..., 周六=7
        // 转换为我们的格式: 周一=1, 周二=2, ..., 周日=7
        if (dayOfWeek == Calendar.SUNDAY) {
            return 7; // 周日
        } else {
            return dayOfWeek - 1; // 周一到周六
        }
    }
    
    /**
     * 获取当前时间的格式化字符串
     * @return HH:mm格式的时间字符串
     */
    public static String getCurrentTimeString() {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm", Locale.CHINA);
        return sdf.format(Calendar.getInstance().getTime());
    }
    
    /**
     * 获取节次对应的默认时间
     * @param period 节次(1-12)
     * @return 时间数组[开始时间, 结束时间]
     */
    public static String[] getDefaultTimeForPeriod(int period) {
        switch (period) {
            case 1:
                return new String[]{"08:00", "08:45"};
            case 2:
                return new String[]{"08:55", "09:40"};
            case 3:
                return new String[]{"10:00", "10:45"};
            case 4:
                return new String[]{"10:55", "11:40"};
            case 5:
                return new String[]{"14:00", "14:45"};
            case 6:
                return new String[]{"14:55", "15:40"};
            case 7:
                return new String[]{"16:00", "16:45"};
            case 8:
                return new String[]{"16:55", "17:40"};
            case 9:
                return new String[]{"19:00", "19:45"};
            case 10:
                return new String[]{"19:55", "20:40"};
            case 11:
                return new String[]{"20:50", "21:35"};
            case 12:
                return new String[]{"21:45", "22:30"};
            default:
                return new String[]{"08:00", "08:45"};
        }
    }
    
    /**
     * 获取节次的显示名称
     * @param period 节次(1-12)
     * @return 节次显示名称
     */
    public static String getPeriodDisplayName(int period) {
        if (period >= 1 && period <= 12) {
            return "第" + period + "节";
        }
        return "第1节";
    }
}

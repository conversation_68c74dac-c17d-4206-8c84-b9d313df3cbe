package com.classschedule.app;

import android.graphics.Color;
import android.os.Bundle;
import android.view.View;
import android.widget.TableLayout;
import android.widget.TableRow;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;

import com.classschedule.app.data.CourseDatabase;
import com.classschedule.app.model.Course;
import com.classschedule.app.utils.DateUtils;
import com.google.android.material.floatingactionbutton.FloatingActionButton;

import java.util.List;

/**
 * 主活动类
 * 负责显示课程表界面和处理用户交互
 */
public class MainActivity extends AppCompatActivity {
    
    private TextView tvCurrentDate;
    private TableLayout tableSchedule;
    private FloatingActionButton fabAddCourse;
    private CourseDatabase courseDatabase;
    
    // 课程表配置
    private static final int MAX_PERIODS = 12; // 最大节次数
    private static final int WEEKDAYS = 5;     // 工作日数量
    
    // 课程背景颜色数组
    private final int[] courseColors = {
        R.color.course_bg_1,
        R.color.course_bg_2,
        R.color.course_bg_3,
        R.color.course_bg_4,
        R.color.course_bg_5
    };
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        initViews();
        initDatabase();
        setupCurrentDate();
        setupScheduleTable();
        setupFab();
        
        loadCourseData();
    }
    
    /**
     * 初始化视图组件
     */
    private void initViews() {
        tvCurrentDate = findViewById(R.id.tv_current_date);
        tableSchedule = findViewById(R.id.table_schedule);
        fabAddCourse = findViewById(R.id.fab_add_course);
    }
    
    /**
     * 初始化数据库
     */
    private void initDatabase() {
        courseDatabase = CourseDatabase.getInstance(this);
    }
    
    /**
     * 设置当前日期显示
     */
    private void setupCurrentDate() {
        String currentDate = DateUtils.getCurrentDateString();
        tvCurrentDate.setText(currentDate);
    }
    
    /**
     * 设置课程表格
     */
    private void setupScheduleTable() {
        tableSchedule.removeAllViews();
        
        // 创建课程表行
        for (int period = 1; period <= MAX_PERIODS; period++) {
            TableRow row = createScheduleRow(period);
            tableSchedule.addView(row);
        }
    }
    
    /**
     * 创建课程表行
     * @param period 节次
     * @return TableRow
     */
    private TableRow createScheduleRow(int period) {
        TableRow row = new TableRow(this);
        row.setLayoutParams(new TableLayout.LayoutParams(
            TableLayout.LayoutParams.MATCH_PARENT,
            TableLayout.LayoutParams.WRAP_CONTENT
        ));
        
        // 添加节次列
        TextView periodCell = createPeriodCell(period);
        row.addView(periodCell);
        
        // 添加星期一到星期五的课程列
        for (int dayOfWeek = 1; dayOfWeek <= WEEKDAYS; dayOfWeek++) {
            TextView courseCell = createCourseCell(dayOfWeek, period);
            row.addView(courseCell);
        }
        
        return row;
    }
    
    /**
     * 创建节次单元格
     * @param period 节次
     * @return TextView
     */
    private TextView createPeriodCell(int period) {
        TextView cell = new TextView(this);
        
        TableRow.LayoutParams params = new TableRow.LayoutParams(0, 120);
        params.weight = 1;
        cell.setLayoutParams(params);
        
        cell.setText(DateUtils.getPeriodDisplayName(period));
        cell.setGravity(android.view.Gravity.CENTER);
        cell.setTextSize(14);
        cell.setTextColor(ContextCompat.getColor(this, R.color.text_primary));
        cell.setBackgroundResource(R.drawable.table_cell_border);
        cell.setPadding(8, 8, 8, 8);
        
        return cell;
    }
    
    /**
     * 创建课程单元格
     * @param dayOfWeek 星期几
     * @param period 节次
     * @return TextView
     */
    private TextView createCourseCell(int dayOfWeek, int period) {
        TextView cell = new TextView(this);
        
        TableRow.LayoutParams params = new TableRow.LayoutParams(0, 120);
        params.weight = 2;
        cell.setLayoutParams(params);
        
        cell.setGravity(android.view.Gravity.CENTER);
        cell.setTextSize(12);
        cell.setTextColor(ContextCompat.getColor(this, R.color.text_primary));
        cell.setBackgroundResource(R.drawable.table_cell_border);
        cell.setPadding(8, 8, 8, 8);
        cell.setClickable(true);
        cell.setFocusable(true);
        
        // 设置标签用于识别位置
        cell.setTag(dayOfWeek + "_" + period);
        
        // 设置点击事件
        cell.setOnClickListener(v -> onCourseCellClick(dayOfWeek, period));
        
        return cell;
    }
    
    /**
     * 设置添加课程按钮
     */
    private void setupFab() {
        fabAddCourse.setOnClickListener(v -> {
            // TODO: 打开添加课程对话框
            Toast.makeText(this, "添加课程功能待实现", Toast.LENGTH_SHORT).show();
        });
    }
    
    /**
     * 加载课程数据
     */
    private void loadCourseData() {
        List<Course> courses = courseDatabase.getAllCourses();
        
        // 清空所有课程单元格
        clearAllCourseCells();
        
        // 填充课程数据
        for (Course course : courses) {
            updateCourseCell(course);
        }
    }
    
    /**
     * 清空所有课程单元格
     */
    private void clearAllCourseCells() {
        for (int i = 0; i < tableSchedule.getChildCount(); i++) {
            TableRow row = (TableRow) tableSchedule.getChildAt(i);
            for (int j = 1; j < row.getChildCount(); j++) { // 跳过第一列(节次列)
                TextView cell = (TextView) row.getChildAt(j);
                cell.setText("");
                cell.setBackgroundResource(R.drawable.table_cell_border);
            }
        }
    }
    
    /**
     * 更新课程单元格
     * @param course 课程信息
     */
    private void updateCourseCell(Course course) {
        TextView cell = findCourseCell(course.getDayOfWeek(), course.getPeriod());
        if (cell != null) {
            cell.setText(course.getDisplayText());
            
            // 设置背景颜色
            int colorIndex = (int) (course.getId() % courseColors.length);
            int backgroundColor = ContextCompat.getColor(this, courseColors[colorIndex]);
            cell.setBackgroundColor(backgroundColor);
        }
    }
    
    /**
     * 查找指定位置的课程单元格
     * @param dayOfWeek 星期几
     * @param period 节次
     * @return TextView
     */
    private TextView findCourseCell(int dayOfWeek, int period) {
        if (period < 1 || period > MAX_PERIODS || dayOfWeek < 1 || dayOfWeek > WEEKDAYS) {
            return null;
        }
        
        TableRow row = (TableRow) tableSchedule.getChildAt(period - 1);
        if (row != null && dayOfWeek < row.getChildCount()) {
            return (TextView) row.getChildAt(dayOfWeek); // dayOfWeek因为第0列是节次列
        }
        
        return null;
    }
    
    /**
     * 课程单元格点击事件
     * @param dayOfWeek 星期几
     * @param period 节次
     */
    private void onCourseCellClick(int dayOfWeek, int period) {
        Course course = courseDatabase.getCourse(dayOfWeek, period);
        
        if (course != null) {
            // 已有课程，显示编辑/删除选项
            showCourseOptionsDialog(course);
        } else {
            // 空白单元格，显示添加课程选项
            showAddCourseDialog(dayOfWeek, period);
        }
    }
    
    /**
     * 显示课程选项对话框
     * @param course 课程信息
     */
    private void showCourseOptionsDialog(Course course) {
        // TODO: 实现课程选项对话框
        String message = "课程：" + course.getCourseName() + 
                        "\n教师：" + course.getTeacherName() +
                        "\n地点：" + course.getClassroom();
        Toast.makeText(this, message, Toast.LENGTH_LONG).show();
    }
    
    /**
     * 显示添加课程对话框
     * @param dayOfWeek 星期几
     * @param period 节次
     */
    private void showAddCourseDialog(int dayOfWeek, int period) {
        // TODO: 实现添加课程对话框
        String message = "在 " + DateUtils.getWeekDayStringByIndex(dayOfWeek) + 
                        " " + DateUtils.getPeriodDisplayName(period) + " 添加课程";
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // 刷新当前日期
        setupCurrentDate();
        // 重新加载课程数据
        loadCourseData();
    }
}

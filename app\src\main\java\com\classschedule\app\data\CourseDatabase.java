package com.classschedule.app.data;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import com.classschedule.app.model.Course;
import java.util.ArrayList;
import java.util.List;

/**
 * 课程数据库管理类
 * 负责课程数据的存储和管理
 */
public class CourseDatabase extends SQLiteOpenHelper {
    
    private static final String DATABASE_NAME = "course_schedule.db";
    private static final int DATABASE_VERSION = 1;
    
    // 表名
    private static final String TABLE_COURSES = "courses";
    
    // 列名
    private static final String COLUMN_ID = "id";
    private static final String COLUMN_COURSE_NAME = "course_name";
    private static final String COLUMN_TEACHER_NAME = "teacher_name";
    private static final String COLUMN_CLASSROOM = "classroom";
    private static final String COLUMN_DAY_OF_WEEK = "day_of_week";
    private static final String COLUMN_PERIOD = "period";
    private static final String COLUMN_TIME_START = "time_start";
    private static final String COLUMN_TIME_END = "time_end";
    private static final String COLUMN_BACKGROUND_COLOR = "background_color";
    
    // 创建表的SQL语句
    private static final String CREATE_TABLE_COURSES = 
        "CREATE TABLE " + TABLE_COURSES + " (" +
        COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
        COLUMN_COURSE_NAME + " TEXT NOT NULL, " +
        COLUMN_TEACHER_NAME + " TEXT, " +
        COLUMN_CLASSROOM + " TEXT, " +
        COLUMN_DAY_OF_WEEK + " INTEGER NOT NULL, " +
        COLUMN_PERIOD + " INTEGER NOT NULL, " +
        COLUMN_TIME_START + " TEXT, " +
        COLUMN_TIME_END + " TEXT, " +
        COLUMN_BACKGROUND_COLOR + " INTEGER DEFAULT 0" +
        ")";
    
    private static CourseDatabase instance;
    
    private CourseDatabase(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }
    
    public static synchronized CourseDatabase getInstance(Context context) {
        if (instance == null) {
            instance = new CourseDatabase(context.getApplicationContext());
        }
        return instance;
    }
    
    @Override
    public void onCreate(SQLiteDatabase db) {
        db.execSQL(CREATE_TABLE_COURSES);
        insertSampleData(db);
    }
    
    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_COURSES);
        onCreate(db);
    }
    
    /**
     * 插入示例数据
     */
    private void insertSampleData(SQLiteDatabase db) {
        // 添加一些示例课程
        insertSampleCourse(db, "语文", "张老师", "101教室", 1, 1, "08:00", "08:45");
        insertSampleCourse(db, "数学", "李老师", "102教室", 1, 2, "08:55", "09:40");
        insertSampleCourse(db, "英语", "王老师", "103教室", 1, 3, "10:00", "10:45");
        insertSampleCourse(db, "物理", "赵老师", "实验室", 2, 1, "08:00", "08:45");
        insertSampleCourse(db, "化学", "钱老师", "化学实验室", 2, 2, "08:55", "09:40");
        insertSampleCourse(db, "生物", "孙老师", "生物实验室", 3, 1, "08:00", "08:45");
        insertSampleCourse(db, "历史", "周老师", "201教室", 3, 2, "08:55", "09:40");
        insertSampleCourse(db, "地理", "吴老师", "202教室", 4, 1, "08:00", "08:45");
        insertSampleCourse(db, "政治", "郑老师", "203教室", 4, 2, "08:55", "09:40");
        insertSampleCourse(db, "体育", "刘老师", "操场", 5, 1, "08:00", "08:45");
    }
    
    private void insertSampleCourse(SQLiteDatabase db, String courseName, String teacherName, 
                                   String classroom, int dayOfWeek, int period, 
                                   String timeStart, String timeEnd) {
        ContentValues values = new ContentValues();
        values.put(COLUMN_COURSE_NAME, courseName);
        values.put(COLUMN_TEACHER_NAME, teacherName);
        values.put(COLUMN_CLASSROOM, classroom);
        values.put(COLUMN_DAY_OF_WEEK, dayOfWeek);
        values.put(COLUMN_PERIOD, period);
        values.put(COLUMN_TIME_START, timeStart);
        values.put(COLUMN_TIME_END, timeEnd);
        values.put(COLUMN_BACKGROUND_COLOR, 0);
        
        db.insert(TABLE_COURSES, null, values);
    }
    
    /**
     * 添加课程
     */
    public long addCourse(Course course) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(COLUMN_COURSE_NAME, course.getCourseName());
        values.put(COLUMN_TEACHER_NAME, course.getTeacherName());
        values.put(COLUMN_CLASSROOM, course.getClassroom());
        values.put(COLUMN_DAY_OF_WEEK, course.getDayOfWeek());
        values.put(COLUMN_PERIOD, course.getPeriod());
        values.put(COLUMN_TIME_START, course.getTimeStart());
        values.put(COLUMN_TIME_END, course.getTimeEnd());
        values.put(COLUMN_BACKGROUND_COLOR, course.getBackgroundColor());
        
        long id = db.insert(TABLE_COURSES, null, values);
        db.close();
        return id;
    }
    
    /**
     * 获取所有课程
     */
    public List<Course> getAllCourses() {
        List<Course> courses = new ArrayList<>();
        SQLiteDatabase db = this.getReadableDatabase();
        
        Cursor cursor = db.query(TABLE_COURSES, null, null, null, null, null, 
                                COLUMN_DAY_OF_WEEK + " ASC, " + COLUMN_PERIOD + " ASC");
        
        if (cursor.moveToFirst()) {
            do {
                Course course = cursorToCourse(cursor);
                courses.add(course);
            } while (cursor.moveToNext());
        }
        
        cursor.close();
        db.close();
        return courses;
    }
    
    /**
     * 根据星期和节次获取课程
     */
    public Course getCourse(int dayOfWeek, int period) {
        SQLiteDatabase db = this.getReadableDatabase();
        Course course = null;
        
        String selection = COLUMN_DAY_OF_WEEK + " = ? AND " + COLUMN_PERIOD + " = ?";
        String[] selectionArgs = {String.valueOf(dayOfWeek), String.valueOf(period)};
        
        Cursor cursor = db.query(TABLE_COURSES, null, selection, selectionArgs, 
                                null, null, null);
        
        if (cursor.moveToFirst()) {
            course = cursorToCourse(cursor);
        }
        
        cursor.close();
        db.close();
        return course;
    }
    
    /**
     * 更新课程
     */
    public int updateCourse(Course course) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(COLUMN_COURSE_NAME, course.getCourseName());
        values.put(COLUMN_TEACHER_NAME, course.getTeacherName());
        values.put(COLUMN_CLASSROOM, course.getClassroom());
        values.put(COLUMN_DAY_OF_WEEK, course.getDayOfWeek());
        values.put(COLUMN_PERIOD, course.getPeriod());
        values.put(COLUMN_TIME_START, course.getTimeStart());
        values.put(COLUMN_TIME_END, course.getTimeEnd());
        values.put(COLUMN_BACKGROUND_COLOR, course.getBackgroundColor());
        
        int rowsAffected = db.update(TABLE_COURSES, values, 
                                   COLUMN_ID + " = ?", 
                                   new String[]{String.valueOf(course.getId())});
        db.close();
        return rowsAffected;
    }
    
    /**
     * 删除课程
     */
    public void deleteCourse(long courseId) {
        SQLiteDatabase db = this.getWritableDatabase();
        db.delete(TABLE_COURSES, COLUMN_ID + " = ?", 
                 new String[]{String.valueOf(courseId)});
        db.close();
    }
    
    /**
     * 将Cursor转换为Course对象
     */
    private Course cursorToCourse(Cursor cursor) {
        Course course = new Course();
        
        course.setId(cursor.getLong(cursor.getColumnIndexOrThrow(COLUMN_ID)));
        course.setCourseName(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_COURSE_NAME)));
        course.setTeacherName(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_TEACHER_NAME)));
        course.setClassroom(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_CLASSROOM)));
        course.setDayOfWeek(cursor.getInt(cursor.getColumnIndexOrThrow(COLUMN_DAY_OF_WEEK)));
        course.setPeriod(cursor.getInt(cursor.getColumnIndexOrThrow(COLUMN_PERIOD)));
        course.setTimeStart(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_TIME_START)));
        course.setTimeEnd(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_TIME_END)));
        course.setBackgroundColor(cursor.getInt(cursor.getColumnIndexOrThrow(COLUMN_BACKGROUND_COLOR)));
        
        return course;
    }
}

# 智能课程表管理应用

## 项目概述

这是一个基于Android原生开发的智能课程表管理应用，专为1920*1080分辨率的平板和大屏手机设计。应用提供直观的课程表界面展示，支持课程信息的增删改查功能。

## 功能特性

### 核心功能
- ✅ **课程表展示**：以表格形式显示每周课程安排
- ✅ **日期显示**：动态显示当前日期和星期
- ✅ **数据存储**：使用SQLite数据库持久化存储课程信息
- ✅ **示例数据**：内置示例课程数据便于演示
- 🔄 **课程管理**：支持添加、编辑、删除课程信息（待完善）
- ✅ **滚动浏览**：支持垂直滚动查看完整课程表

### 界面特点
- 顶部显示当前日期（格式：周X X月X号）
- 主体为可滚动的课程表格
- 表格支持12节课×5个工作日显示
- 界面简洁美观，适配横屏显示
- 响应式布局，适配1920*1080分辨率

## 技术架构

### 开发环境
- **平台**：Android原生应用
- **最低SDK版本**：API 21 (Android 5.0)
- **目标SDK版本**：API 33 (Android 13)
- **开发语言**：Java
- **构建工具**：Gradle 8.0.2

### 核心技术
- **布局框架**：ConstraintLayout + TableLayout
- **数据库**：SQLite (自定义DatabaseHelper)
- **UI组件**：Material Design Components
- **架构模式**：MVC模式

### 项目结构
```
app/
├── src/main/
│   ├── java/com/classschedule/app/
│   │   ├── MainActivity.java           # 主活动
│   │   ├── model/
│   │   │   └── Course.java            # 课程实体类
│   │   ├── data/
│   │   │   └── CourseDatabase.java    # 数据库管理类
│   │   └── utils/
│   │       └── DateUtils.java         # 日期工具类
│   ├── res/
│   │   ├── layout/
│   │   │   └── activity_main.xml      # 主界面布局
│   │   ├── values/
│   │   │   ├── colors.xml             # 颜色定义
│   │   │   ├── strings.xml            # 字符串资源
│   │   │   └── themes.xml             # 主题样式
│   │   └── drawable/
│   │       ├── table_cell_border.xml  # 表格边框样式
│   │       └── ic_add.xml             # 添加图标
│   └── AndroidManifest.xml            # 应用清单
├── build.gradle                       # 应用构建配置
└── proguard-rules.pro                 # 混淆规则
```

## 数据模型

### Course 课程实体
```java
public class Course {
    private long id;                // 课程ID
    private String courseName;      // 课程名称
    private String teacherName;     // 任课教师
    private String classroom;       // 上课地点
    private int dayOfWeek;         // 星期几 (1-7, 1为周一)
    private int period;            // 第几节课 (1-12)
    private String timeStart;      // 开始时间
    private String timeEnd;        // 结束时间
    private int backgroundColor;   // 背景颜色
}
```

### 数据库表结构
```sql
CREATE TABLE courses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    course_name TEXT NOT NULL,
    teacher_name TEXT,
    classroom TEXT,
    day_of_week INTEGER NOT NULL,
    period INTEGER NOT NULL,
    time_start TEXT,
    time_end TEXT,
    background_color INTEGER DEFAULT 0
);
```

## 界面设计

### 主界面布局
- **顶部区域**：当前日期显示（40sp大字体）
- **中部区域**：可滚动的课程表格
  - 表头：节次 + 周一到周五
  - 表体：12行×6列的课程网格
- **底部区域**：悬浮添加按钮

### 样式规范
- **字体大小**：标题40sp，内容14-16sp
- **颜色方案**：
  - 主文字：黑色 (#000000)
  - 背景色：浅灰色 (#F5F5F5)
  - 表格边框：灰色 (#CCCCCC)
  - 课程背景：5种预设颜色循环使用
- **间距标准**：20dp顶部边距，16dp侧边距

## 已实现功能

### ✅ 基础界面
- [x] 完成XML布局设计
- [x] 实现日期显示功能
- [x] 创建基础表格结构
- [x] 适配横屏显示

### ✅ 数据展示
- [x] 实现课程数据绑定
- [x] 完善表格内容显示
- [x] 添加滚动功能
- [x] 内置示例数据

### ✅ 数据管理
- [x] SQLite数据库设计
- [x] 课程CRUD操作接口
- [x] 数据持久化存储

## 待完善功能

### 🔄 交互功能
- [ ] 课程添加对话框
- [ ] 课程编辑对话框
- [ ] 课程删除确认
- [ ] 时间选择器
- [ ] 颜色选择器

### 🔄 用户体验优化
- [ ] 长按菜单
- [ ] 拖拽排课
- [ ] 课程冲突检测
- [ ] 数据导入导出
- [ ] 主题切换

## 安装说明

1. **环境要求**
   - Android Studio Arctic Fox 或更高版本
   - Android SDK API 21-33
   - Java 8 或更高版本

2. **构建步骤**
   ```bash
   # 克隆项目
   git clone [项目地址]
   
   # 打开Android Studio
   # File -> Open -> 选择项目目录
   
   # 同步Gradle
   # 点击 "Sync Now" 或 Tools -> Android -> Sync Project with Gradle Files
   
   # 运行应用
   # 点击运行按钮或按 Shift+F10
   ```

3. **设备要求**
   - Android 5.0 (API 21) 或更高版本
   - 推荐分辨率：1920×1080
   - 推荐横屏使用

## 使用说明

1. **查看课程表**
   - 启动应用后自动显示当前周的课程安排
   - 顶部显示当前日期
   - 表格显示周一到周五的课程

2. **课程交互**
   - 点击课程单元格查看详细信息
   - 点击空白单元格可添加新课程
   - 使用悬浮按钮快速添加课程

3. **界面操作**
   - 垂直滚动查看所有节次
   - 横屏显示获得最佳体验

## 开发计划

### 第一阶段 ✅ (已完成)
- [x] 基础界面搭建
- [x] 数据模型设计
- [x] 数据库实现
- [x] 基本显示功能

### 第二阶段 🔄 (进行中)
- [ ] 完善交互功能
- [ ] 添加课程管理对话框
- [ ] 优化用户体验

### 第三阶段 📋 (计划中)
- [ ] 高级功能实现
- [ ] 性能优化
- [ ] 测试完善

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

---

**注意**：这是一个教育项目，主要用于学习Android开发。在生产环境使用前请进行充分测试。

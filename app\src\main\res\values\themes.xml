<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.ClassSchedule" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
    
    <!-- 表格单元格样式 -->
    <style name="TableCellStyle">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">80dp</item>
        <item name="android:gravity">center</item>
        <item name="android:padding">8dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:background">@drawable/table_cell_border</item>
    </style>
    
    <!-- 课程单元格样式 -->
    <style name="CourseCellStyle" parent="TableCellStyle">
        <item name="android:textStyle">bold</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
    </style>
    
    <!-- 节次单元格样式 -->
    <style name="PeriodCellStyle" parent="TableCellStyle">
        <item name="android:background">@color/table_header_bg</item>
        <item name="android:textStyle">bold</item>
    </style>
</resources>

// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        google()
        mavenCentral()
        jcenter() // 旧版本可能需要
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:4.2.2'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        jcenter() // 旧版本可能需要
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

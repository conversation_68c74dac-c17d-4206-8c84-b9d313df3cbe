package com.classschedule.app.model;

/**
 * 课程实体类
 * 用于存储课程的基本信息
 */
public class Course {
    private long id;
    private String courseName;      // 课程名称
    private String teacherName;     // 任课教师
    private String classroom;       // 上课地点
    private int dayOfWeek;         // 星期几 (1-7, 1为周一)
    private int period;            // 第几节课 (1-12)
    private String timeStart;      // 开始时间
    private String timeEnd;        // 结束时间
    private int backgroundColor;   // 背景颜色

    public Course() {
    }

    public Course(String courseName, String teacherName, String classroom, 
                  int dayOfWeek, int period, String timeStart, String timeEnd) {
        this.courseName = courseName;
        this.teacherName = teacherName;
        this.classroom = classroom;
        this.dayOfWeek = dayOfWeek;
        this.period = period;
        this.timeStart = timeStart;
        this.timeEnd = timeEnd;
    }

    // Getter 和 Setter 方法
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }

    public String getTeacherName() {
        return teacherName;
    }

    public void setTeacherName(String teacherName) {
        this.teacherName = teacherName;
    }

    public String getClassroom() {
        return classroom;
    }

    public void setClassroom(String classroom) {
        this.classroom = classroom;
    }

    public int getDayOfWeek() {
        return dayOfWeek;
    }

    public void setDayOfWeek(int dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }

    public int getPeriod() {
        return period;
    }

    public void setPeriod(int period) {
        this.period = period;
    }

    public String getTimeStart() {
        return timeStart;
    }

    public void setTimeStart(String timeStart) {
        this.timeStart = timeStart;
    }

    public String getTimeEnd() {
        return timeEnd;
    }

    public void setTimeEnd(String timeEnd) {
        this.timeEnd = timeEnd;
    }

    public int getBackgroundColor() {
        return backgroundColor;
    }

    public void setBackgroundColor(int backgroundColor) {
        this.backgroundColor = backgroundColor;
    }

    /**
     * 获取显示文本
     * @return 格式化的课程信息
     */
    public String getDisplayText() {
        StringBuilder sb = new StringBuilder();
        sb.append(courseName);
        if (teacherName != null && !teacherName.isEmpty()) {
            sb.append("\n").append(teacherName);
        }
        if (classroom != null && !classroom.isEmpty()) {
            sb.append("\n").append(classroom);
        }
        if (timeStart != null && timeEnd != null) {
            sb.append("\n").append(timeStart).append("-").append(timeEnd);
        }
        return sb.toString();
    }

    @Override
    public String toString() {
        return "Course{" +
                "id=" + id +
                ", courseName='" + courseName + '\'' +
                ", teacherName='" + teacherName + '\'' +
                ", classroom='" + classroom + '\'' +
                ", dayOfWeek=" + dayOfWeek +
                ", period=" + period +
                ", timeStart='" + timeStart + '\'' +
                ", timeEnd='" + timeEnd + '\'' +
                '}';
    }
}
